<template>
  <view class="scenic-detail">
    <!-- 顶部图片轮播 -->
    <view class="hero-section">
      <swiper
        class="hero-swiper"
        :indicator-dots="true"
        :autoplay="true"
        :interval="3000"
        :duration="500"
      >
        <swiper-item v-for="(image, index) in scenicInfo.images" :key="index">
          <image class="hero-image" :src="image" mode="aspectFill"></image>
        </swiper-item>
      </swiper>

      <!-- 返回按钮 -->
      <view class="back-btn" @click="goBack">
        <view class="back-arrow"></view>
      </view>
    </view>

    <!-- 景区基本信息 -->
    <view class="info-section">
      <view class="basic-info">
        <text class="scenic-name">{{ scenicInfo.name }}</text>
        <view class="rating-distance">
          <view class="distance">
            <text class="distance-text">距您约{{ scenicInfo.distance }}</text>
          </view>
        </view>
        <text class="scenic-desc">{{ scenicInfo.description }}</text>
      </view>

      <!-- 快捷操作按钮 -->
      <view class="action-buttons">
        <view class="action-btn primary" @click="handleCheckin">
          <view class="video-icon"></view>
          <text class="btn-text">获取视频</text>
        </view>
        <view class="action-btn secondary" @click="handleNavigation">
          <view class="nav-icon"></view>
          <text class="btn-text">导航</text>
        </view>
      </view>
    </view>

    <!-- 景区详细信息 -->
    <view class="detail-sections">
      <!-- 地址信息 -->
      <view class="detail-card">
        <view class="card-header">
          <text class="card-title">详细地址</text>
        </view>
        <text class="card-content">{{ scenicInfo.address }}</text>
      </view>

      <!-- 视频模版选择 -->
      <view class="template-selector-wrapper">
        <template-selector
          v-model="selectedTemplate"
          placeholder="点击选择模版"
          @update:modelValue="handleTemplateChange"
        />
      </view>

      <view class="template-actions">
        <button
          class="create-btn"
          :disabled="!selectedTemplate"
          @click="goToVideoCreation"
        >
          <text class="btn-text">开始创作</text>
        </button>
      </view>
    </view>

    <!-- 底部安全区域 -->
    <view class="safe-area"></view>
  </view>
</template>

<script setup>
import { reactive, ref, onMounted } from "vue";
import TemplateSelector from "@/components/platform/template-selector.vue";

// 选中的模版
const selectedTemplate = ref(null);

// 景区信息
const scenicInfo = reactive({
  id: 1,
  name: "中国孙子文化园",
  rating: 4.8,
  reviewCount: 1256,
  distance: "126.7km",
  description:
    "中国孙子文化园是以春秋时期伟大军事家孙武及其《孙子兵法》为主题的文化园区，集文化展示、休闲娱乐、教育体验于一体。园区内设有孙子文化展览馆、兵法体验区、古战场复原等多个主题区域。",
  images: [
    "https://psyangji.com/wp-content/uploads/2019/12/141e67e02cc755.jpg",
    "https://js.design/special/img/appletpage-design/1665716999860.png",
    "https://js.design/special/img/appletpage-design/1665716999911.png",
  ],
  openTime: "08:00-18:00（全年开放）",
  tickets: [
    { type: "成人票", price: 80 },
    { type: "学生票", price: 40 },
    { type: "儿童票", price: 20 },
  ],
  phone: "0531-12345678",
  address: "山东省济南市历城区孙子文化园路1号",
});

// 获取页面参数
const getPageParams = () => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};

  if (options.id) {
    scenicInfo.id = parseInt(options.id);
  }
  if (options.name) {
    scenicInfo.name = decodeURIComponent(options.name);
  }

  // 根据ID加载不同的景区数据
  loadScenicData(scenicInfo.id);
};

// 根据ID加载景区数据
const loadScenicData = (id) => {
  const scenicData = {
    1: {
      name: "中国孙子文化园",
      rating: 4.8,
      reviewCount: 1256,
      distance: "126.7km",
      description:
        "中国孙子文化园是以春秋时期伟大军事家孙武及其《孙子兵法》为主题的文化园区，集文化展示、休闲娱乐、教育体验于一体。",
      openTime: "08:00-18:00（全年开放）",
      tickets: [
        { type: "成人票", price: 80 },
        { type: "学生票", price: 40 },
        { type: "儿童票", price: 20 },
      ],
      phone: "0531-12345678",
      address: "山东省济南市历城区孙子文化园路1号",
    },
    2: {
      name: "夫子山风景区",
      rating: 4.6,
      reviewCount: 892,
      distance: "282.1km",
      description:
        "夫子山风景区以其秀美的自然风光和深厚的文化底蕴而闻名，是集山水观光、文化体验、休闲度假于一体的综合性景区。",
      openTime: "07:30-17:30（全年开放）",
      tickets: [
        { type: "成人票", price: 60 },
        { type: "学生票", price: 30 },
        { type: "儿童票", price: 15 },
      ],
      phone: "0531-87654321",
      address: "山东省济南市长清区夫子山路88号",
    },
    // 可以继续添加更多景区数据
  };

  const data = scenicData[id] || scenicData[1];
  Object.assign(scenicInfo, { id, ...data });
};

// 页面方法
const goBack = () => {
  uni.navigateBack();
};

const handleCheckin = () => {
  console.log("获取视频，跳转到模板合成界面");
  // 跳转到模板合成界面，传递景区信息
  uni.navigateTo({
    url: `/pages_workbench/pages/videoEdit/index?scenicId=${
      scenicInfo.id
    }&scenicName=${encodeURIComponent(scenicInfo.name)}&from=scenic`,
  });
};

const handleNavigation = () => {
  console.log("导航到景区");
  // 这里可以添加导航功能
};

// 处理模版选择变化
const handleTemplateChange = (template) => {
  console.log("选择模版:", template);

  if (template) {
    // 计算该模板需要的视频个数
    const requiredVideoCount = getMaxMediaCountForTemplate(template);
    console.log(`模板 "${template.Name || template.name}" 需要 ${requiredVideoCount} 个视频`);

    // 显示提示信息
    if (requiredVideoCount === 0) {
      uni.showToast({
        title: '该模板无需上传视频',
        icon: 'none',
        duration: 2000
      });
    } else if (requiredVideoCount > 1) {
      uni.showToast({
        title: `该模板需要${requiredVideoCount}个视频`,
        icon: 'none',
        duration: 2000
      });
    } else {
      uni.showToast({
        title: '该模板需要1个视频',
        icon: 'none',
        duration: 2000
      });
    }
  }
};

const getMaxMediaCountForTemplate = (template) => {
  if (!template || !template.ClipsParam) {
    return 0;
  }

  try {
    const clipsParam = JSON.parse(template.ClipsParam);
    return Object.values(clipsParam).filter((value) => value === "mediaId")
      .length;
  } catch (e) {
    return 0;
  }
};


// 跳转到视频创作页面
const goToVideoCreation = () => {
  console.log("点击开始创作按钮");
  console.log("selectedTemplate.value:", selectedTemplate.value);

  if (!selectedTemplate.value) {
    console.log("没有选择模版，显示提示");
    uni.showToast({
      title: "请先选择模版",
      icon: "none",
    });
    return;
  }

  console.log("开始创作，使用模版:", selectedTemplate.value);
  console.log("景区信息:", scenicInfo);

  // 计算该模板需要的视频个数
  const requiredVideoCount = getMaxMediaCountForTemplate(selectedTemplate.value);
  console.log("该模板需要的视频个数:", requiredVideoCount);

  const targetUrl = `/pages_workbench/pages/videoCreation/index?scenicId=${
    scenicInfo.id
  }&scenicName=${encodeURIComponent(scenicInfo.name)}&templateId=${
    selectedTemplate.value.TemplateId || selectedTemplate.value.id
  }&templateName=${encodeURIComponent(
    selectedTemplate.value.Name || selectedTemplate.value.name
  )}&requiredVideoCount=${requiredVideoCount}&clipsParam=${encodeURIComponent(
    JSON.stringify(selectedTemplate.value.ClipsParam || {})
  )}`;

  console.log("跳转URL:", targetUrl);

  // 跳转到视频创作页面，传递景区信息、模版信息和视频个数要求
  uni.navigateTo({
    url: targetUrl,
    success: (res) => {
      console.log("跳转成功:", res);
    },
    fail: (err) => {
      console.error("跳转失败:", err);
      uni.showToast({
        title: "页面跳转失败",
        icon: "none"
      });
    }
  });
};

// 页面加载时获取景区详情
onMounted(() => {
  getPageParams();
  console.log("当前景区信息:", scenicInfo);
});
</script>

<style lang="scss" scoped>
.scenic-detail {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 顶部图片轮播 */
.hero-section {
  position: relative;
  height: 300px;

  .hero-swiper {
    width: 100%;
    height: 100%;

    .hero-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .back-btn {
    position: absolute;
    top: 44px;
    left: 16px;
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    z-index: 10;

    .back-arrow {
      width: 12px;
      height: 12px;
      border-left: 2px solid #fff;
      border-bottom: 2px solid #fff;
      transform: rotate(45deg);
      margin-left: 2px;
    }
  }
}

/* 基本信息区域 */
.info-section {
  background: #fff;
  margin-top: -20px;
  border-radius: 20px 20px 0 0;
  padding: 24px 20px;
  position: relative;
  z-index: 5;

  .basic-info {
    margin-bottom: 24px;

    .scenic-name {
      font-size: 24px;
      font-weight: 700;
      color: #333;
      margin-bottom: 12px;
      display: block;
    }

    .rating-distance {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .rating {
        display: flex;
        align-items: center;
        gap: 4px;

        .rating-text {
          font-size: 14px;
          font-weight: 600;
          color: #333;
        }

        .review-count {
          font-size: 12px;
          color: #999;
        }
      }

      .distance {
        display: flex;
        align-items: center;
        gap: 4px;

        .distance-text {
          font-size: 12px;
          color: #999;
        }
      }
    }

    .scenic-desc {
      font-size: 14px;
      color: #666;
      line-height: 1.6;
      display: block;
    }
  }

  .action-buttons {
    display: flex;
    gap: 12px;

    .action-btn {
      flex: 1;
      height: 48px;
      border-radius: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      transition: all 0.3s ease;

      &.primary {
        background: linear-gradient(135deg, #9b97e8 0%, #8a85d9 100%);

        .btn-text {
          color: #333;
          font-weight: 600;
        }

        &:active {
          transform: scale(0.98);
        }
      }

      &.secondary {
        background: #f8f9fa;
        border: 1px solid #e9ecef;

        .btn-text {
          color: #666;
          font-weight: 500;
        }

        &:active {
          background: #e9ecef;
        }
      }

      .btn-text {
        font-size: 14px;
      }

      /* CSS导航图标 */
      .nav-icon {
        width: 18px;
        height: 18px;
        position: relative;

        &::before {
          content: "";
          position: absolute;
          top: 2px;
          left: 50%;
          transform: translateX(-50%);
          width: 8px;
          height: 8px;
          border: 2px solid #6d69cd;
          border-radius: 50%;
        }

        &::after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 0;
          height: 0;
          border-left: 4px solid transparent;
          border-right: 4px solid transparent;
          border-top: 6px solid #6d69cd;
        }
      }

      /* CSS视频图标 - 现代播放按钮 */
      .video-icon {
        width: 20px;
        height: 20px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 50%;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15),
          0 2px 6px rgba(109, 105, 205, 0.2);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.1);
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2),
            0 3px 8px rgba(109, 105, 205, 0.3);
        }

        /* 外圆环 */
        &::before {
          content: "";
          position: absolute;
          width: 18px;
          height: 18px;
          background: linear-gradient(135deg, #6d69cd 0%, #5a56b8 100%);
          border-radius: 50%;
          box-shadow: inset 0 1px 2px rgba(255, 255, 255, 0.3);
        }

        /* 播放三角形 */
        &::after {
          content: "";
          position: absolute;
          width: 0;
          height: 0;
          border-left: 6px solid #fff;
          border-top: 4px solid transparent;
          border-bottom: 4px solid transparent;
          margin-left: 2px;
          z-index: 1;
          filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
        }
      }
    }
  }
}

/* 详细信息区域 */
.detail-sections {
  padding: 0 20px 20px 20px;

  .detail-card {
    background: #fff;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    margin-top: 30rpx;

    .card-header {
      display: flex;
      flex-direction: column;
      gap: 4px;
      margin-bottom: 16px;

      .card-title {
        font-size: 18px;
        font-weight: 700;
        color: #333;
      }

      .card-subtitle {
        font-size: 14px;
        color: #666;
        font-weight: 400;
      }
    }

    .card-content {
      font-size: 14px;
      color: #666;
      line-height: 1.5;
      display: block;
    }

    .ticket-info {
      .ticket-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .ticket-type {
          font-size: 14px;
          color: #333;
        }

        .ticket-price {
          font-size: 16px;
          font-weight: 600;
          color: #9aff02;
        }
      }
    }
  }
}

/* 模版选择器包装样式 */
.template-selector-wrapper {
  width: calc(100% + 40rpx);
  margin: 0 auto;
  margin-left: -40rpx;
  margin-right: -20rpx;
  padding: 0 20rpx;
  box-sizing: border-box;

  /* 确保内部组件占满宽度 */
  :deep(.template-selector) {
    width: 100% !important;
  }

  :deep(.nav-item) {
    width: 100% !important;
    min-width: unset !important;
  }

  :deep(.nav-item-content) {
    padding: 16rpx 24rpx !important;
  }
}

/* 开始创作按钮样式 */
.template-actions {
  margin-top: 24rpx;
  padding: 0 8rpx;

  .create-btn {
    width: 100%;
    height: 96rpx;
    background: linear-gradient(135deg, #6D69CD 0%, #5A56B8 100%);
    border: none;
    border-radius: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8rpx 24rpx rgba(109, 105, 205, 0.3);
    position: relative;
    overflow: hidden;

    /* 光泽动画效果 */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s ease;
    }

    &:hover::before {
      left: 100%;
    }

    &:disabled {
      background: linear-gradient(135deg, #e0e0e0 0%, #d0d0d0 100%);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
      cursor: not-allowed;

      &::before {
        display: none;
      }

      .btn-text {
        color: #999 !important;
      }
    }

    &:not(:disabled):active {
      transform: scale(0.98);
      box-shadow: 0 4rpx 16rpx rgba(109, 105, 205, 0.4);
    }

    .btn-text {
      font-size: 32rpx;
      font-weight: 700;
      color: white;
      position: relative;
      z-index: 1;
      letter-spacing: 2rpx;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    }


  }
}

/* 底部安全区域 */
.safe-area {
  height: calc(20px + env(safe-area-inset-bottom));
  background: #f8f9fa;
}
</style>
