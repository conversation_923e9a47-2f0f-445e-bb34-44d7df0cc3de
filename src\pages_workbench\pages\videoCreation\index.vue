<template>
  <view class="video-creation">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="back-btn" @click="goBack">
        <view class="back-arrow"></view>
      </view>
      <text class="header-title">视频创作</text>
      <view class="header-placeholder"></view>
    </view>

    <!-- 模版信息展示 -->
    <view class="template-info">
      <view class="template-preview">
        <image :src="templateInfo.preview" mode="aspectFill" class="template-image"></image>
        <view class="template-overlay">
          <view class="play-icon">
            <text class="play-symbol">▶</text>
          </view>
        </view>
      </view>
      <view class="template-details">
        <text class="template-name">{{ templateInfo.name }}</text>
        <text class="template-desc">{{ templateInfo.description }}</text>
        <view class="template-tags">
          <text class="tag">{{ templateInfo.duration }}</text>
          <text class="tag">{{ templateInfo.style }}</text>
          <text class="tag video-count-tag" v-if="getMaxMediaCount() > 0">
            需要{{ getMaxMediaCount() }}个视频
          </text>
          <text class="tag video-count-tag" v-else>
            无需上传视频
          </text>
        </view>
      </view>
    </view>

    <!-- 任务信息表单 -->
    <view class="form-section">
      <view class="form-card">
        <view class="form-header">
          <text class="form-title">任务信息</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">任务名称</text>
          <input 
            class="form-input" 
            v-model="taskInfo.name" 
            placeholder="请输入任务名称"
            maxlength="50"
          />
        </view>
        
        <view class="form-item">
          <text class="form-label">任务描述</text>
          <textarea 
            class="form-textarea" 
            v-model="taskInfo.description" 
            placeholder="请描述您想要创作的视频内容..."
            maxlength="200"
          />
        </view>
      </view>
    </view>

    <!-- 视频上传区域 -->
    <view class="upload-section">
      <VideoUploadCard
        v-model="videoList"
        :max-count="getMaxMediaCount()"
        :has-template="!!templateInfo.id"
        :template-name="templateInfo.name || ''"
        :disabled="isUploadDisabled"
        :is-uploading="isUploading"
        :upload-progress="uploadProgress"
        @after-read="afterRead"
        @delete="deletePic"
        @upload="handleUploadTrigger"
        @preview="handleVideoPreview"
      />
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <button 
        class="create-btn" 
        :disabled="!canCreate"
        @click="startCreation"
      >
        <text class="btn-text">开始创作</text>
      </button>
    </view>

    <!-- 底部安全区域 -->
    <view class="safe-area"></view>
  </view>
</template>

<script setup>
import { reactive, ref, computed } from 'vue'
import VideoUploadCard from "@/components/platform/video-upload-card.vue"
import { onLoad } from '@dcloudio/uni-app'
import modal from "@/plugins/modal"
import { synthesizeVideo } from "@/api/platform/videoEdit"

// 模版信息
const templateInfo = reactive({
  id: 0,
  name: '默认名称',
  description: '默认描述',
  previewUrl: '',
  duration: '15s',
  requiredVideoCount: 0
})

// 景区信息
const scenicInfo = reactive({
  id: 0,
  name: '默认名称'
})

// 任务信息
const taskInfo = reactive({
  name: '',
  description: ''
})

// 视频列表
const videoList = ref([])

// 上传相关状态
const isUploading = ref(false)
const uploadProgress = ref(0)


// 页面加载时获取参数
onLoad((options) => {
  console.log('接收到的页面参数:', options)
  scenicInfo.id = options.scenicId
  scenicInfo.name = decodeURIComponent(options.scenicName)
  templateInfo.id = options.templateId
  templateInfo.name = decodeURIComponent(options.templateName)
  templateInfo.requiredVideoCount = parseInt(options.requiredVideoCount)

  // 根据模版ID加载模版数据
  // loadTemplateData(templateInfo.id)  这里可以获取模板的播放链接，实现播放

  // 设置默认任务名称（景区 + 模板名称）
  taskInfo.name = `${scenicInfo.name} - ${templateInfo.name}`
})



// 计算是否可以创作
const canCreate = computed(() => {
  // 检查是否有模板
  if (!templateInfo.id) {
    return false
  }

  const requiredVideoCount = getMaxMediaCount()
  const currentVideoCount = videoList.value.length

  // 检查基本信息
  if (!taskInfo.name.trim() || !taskInfo.description.trim()) {
    return false
  }

  // 如果模板不需要视频（返回0），可以直接创作
  if (requiredVideoCount === 0) {
    return true
  }

  // 必须上传完全匹配数量的视频才能创作
  return currentVideoCount === requiredVideoCount
})

// 计算是否禁用上传组件
const isUploadDisabled = computed(() => {
  return isUploading.value
})



// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 计算最大允许上传的媒体文件数量
const getMaxMediaCount = () => {
  // 优先使用从上一个页面传递过来的参数
  if (templateInfo.requiredVideoCount) {
    console.log('使用传递的视频个数:', templateInfo.requiredVideoCount)
    return templateInfo.requiredVideoCount
  }

  return 0 
}

// 处理文件上传后的回调
const afterRead = (event) => {
  const { file } = event
  const fileList = Array.isArray(file) ? file : [file]

  fileList.forEach((item) => {
    const videoFile = {
      ...item,
      name: item.name || `视频_${Date.now()}`,
      size: item.size || 0,
      type: item.type || "video/mp4",
      url: item.url || item.path,
      path: item.path,
    }
    videoList.value.push(videoFile)
  })
}

// 删除已上传的视频
const deletePic = (file, index) => {
  try {
    if (typeof index === "number" && index >= 0 && index < videoList.value.length) {
      videoList.value.splice(index, 1)
      uni.showToast({
        title: '删除成功',
        icon: 'success'
      })
    }
  } catch (e) {
    uni.showToast({
      title: '删除失败',
      icon: 'none'
    })
  }
}

// 处理上传触发事件
const handleUploadTrigger = () => {
  // 可以在这里添加上传前的处理逻辑
}

// 处理视频预览
const handleVideoPreview = (file, index) => {
  if (!file.url) return

  // 这里可以添加视频预览逻辑
  console.log('预览视频:', file)
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 表单验证
const validateForm = () => {
  if (!templateInfo.id) {
    modal.msg("请选择模板")
    return false
  }

  const requiredVideoCount = getMaxMediaCount()
  const currentVideoCount = videoList.value.length

  // 如果模板不需要视频，直接通过验证
  if (requiredVideoCount === 0) {
    return true
  }

  // 检查是否有上传视频
  if (currentVideoCount === 0) {
    modal.msg(`请上传视频，当前模板需要 ${requiredVideoCount} 个视频`)
    return false
  }

  // 检查视频数量是否不足
  if (currentVideoCount < requiredVideoCount) {
    modal.msg(
      `视频数量不足，还需要上传 ${
        requiredVideoCount - currentVideoCount
      } 个视频 (当前 ${currentVideoCount}/${requiredVideoCount})`
    )
    return false
  }

  // 检查视频数量是否过多
  if (currentVideoCount > requiredVideoCount) {
    modal.msg(
      `视频数量过多，请删除 ${
        currentVideoCount - requiredVideoCount
      } 个多余的视频 (当前 ${currentVideoCount}/${requiredVideoCount})`
    )
    return false
  }

  // 检查任务信息
  if (!taskInfo.name.trim()) {
    modal.msg("请输入任务名称")
    return false
  }

  if (!taskInfo.description.trim()) {
    modal.msg("请输入任务描述")
    return false
  }

  return true
}

// 开始创作（视频合成）
const startCreation = async () => {
  if (!validateForm()) return

  modal.loading("提交中...")

  try {
    const files = []

    videoList.value.forEach((item) => {
      files.push({
        name: item.name,
        uri: item.url || item.path,
      })
    })

    // 拼装项目元数据
    const projectMetadata = {
      Title: taskInfo.name?.trim() || "",
      Description: taskInfo.description?.trim() || "",
      ScenicId: scenicInfo.id,
      ScenicName: scenicInfo.name
    }

    const params = {
      templateId: templateInfo.id,
      clipsParam: templateInfo.ClipsParam,
      files: files,
      projectMetadata: JSON.stringify(projectMetadata)
    }

    console.log('开始创作，发送参数:', params)

    const res = await synthesizeVideo(params)

    modal.closeLoading()

    // 显示成功提示并自动返回
    uni.showToast({
      title: "创作任务已提交",
      icon: "success",
      duration: 2000,
      success: () => {
        // 成功提示显示后自动返回
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      },
    })
  } catch (e) {
    modal.closeLoading()

    // 显示更友好的错误信息
    const errorMessage =
      e?.message || e?.data?.message || "创作失败，请稍后重试"
    modal.msg(errorMessage)

    // 可以选择是否在失败时也提供返回选项
    setTimeout(() => {
      uni.showModal({
        title: "创作失败",
        content: "是否返回上一页？",
        confirmText: "返回",
        cancelText: "留在此页",
        success: (res) => {
          if (res.confirm) {
            uni.navigateBack()
          }
        },
      })
    }, 2000)
  }
}

// 页面加载完成
console.log('视频创作页面初始化完成')
</script>

<style lang="scss" scoped>
.video-creation {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 20px 20px 20px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .back-btn {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:active {
      background: #e0e0e0;
      transform: scale(0.95);
    }

    .back-arrow {
      width: 12px;
      height: 12px;
      border-left: 2px solid #333;
      border-bottom: 2px solid #333;
      transform: rotate(45deg);
      margin-left: 2px;
    }
  }

  .header-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .header-placeholder {
    width: 40px;
  }
}

/* 模版信息展示 */
.template-info {
  display: flex;
  padding: 20px;
  background: #fff;
  margin-bottom: 12px;
  gap: 16px;

  .template-preview {
    position: relative;
    width: 100px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;

    .template-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .template-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;

      .play-icon {
        width: 24px;
        height: 24px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        .play-symbol {
          font-size: 10px;
          color: #6D69CD;
          margin-left: 1px;
        }
      }
    }
  }

  .template-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;

    .template-name {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .template-desc {
      font-size: 14px;
      color: #666;
    }

    .template-tags {
      display: flex;
      gap: 8px;
      margin-top: 8px;

      .tag {
        padding: 4px 8px;
        background: rgba(109, 105, 205, 0.1);
        color: #6D69CD;
        font-size: 12px;
        border-radius: 12px;

        &.video-count-tag {
          background: linear-gradient(135deg, #6D69CD 0%, #5A56B8 100%);
          color: white;
          font-weight: 600;
        }
      }
    }
  }
}

/* 表单区域 */
.form-section {
  padding: 0 20px;
  margin-bottom: 12px;

  .form-card {
    background: #fff;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

    .form-header {
      margin-bottom: 20px;

      .form-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
    }

    .form-item {
      margin-bottom: 20px;

      .form-label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;
      }

      .form-input {
        width: 100%;
        height: 48px;
        padding: 0 16px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        font-size: 16px;
        color: #333;
        background: #fff;
        box-sizing: border-box;

        &:focus {
          border-color: #6D69CD;
          outline: none;
        }
      }

      .form-textarea {
        width: 100%;
        min-height: 80px;
        padding: 12px 16px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        font-size: 16px;
        color: #333;
        background: #fff;
        resize: none;
        box-sizing: border-box;

        &:focus {
          border-color: #6D69CD;
          outline: none;
        }
      }
    }
  }
}

/* 上传区域 */
.upload-section {
  padding: 0 20px;
  margin-bottom: 20px;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
  z-index: 100;

  .create-btn {
    width: 100%;
    height: 52px;
    background: linear-gradient(135deg, #9B97E8 0%, #8A85D9 100%);
    border: none;
    border-radius: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:disabled {
      background: #e0e0e0;
      cursor: not-allowed;
    }

    &:not(:disabled):active {
      transform: scale(0.98);
    }

    .btn-text {
      font-size: 18px;
      font-weight: 600;
      color: white;
    }
  }
}

/* 底部安全区域 */
.safe-area {
  height: calc(92px + env(safe-area-inset-bottom));
  background: #f8f9fa;
}
</style>
